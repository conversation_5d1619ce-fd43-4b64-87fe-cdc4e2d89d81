COMBINED_ANALYSIS_PROMPT = """
Analyze the following content and provide three analyses:

1. TRANSLATION: {translation_instructions}

2. SOPHISTICATION: {sophistication_instructions}

3. USE_CASE: {use_case_instructions}

Content: {{content}}

Respond in this exact format:
TRANSLATION: [result]
SOPHISTICATION: [result]  
USE_CASE: [result]
""".format(
    translation_instructions="Determine its language. If it's not in English, translate it to English and prefix the translation with \"[X]\" where X is the original language code. If already in English, return unchanged.",
    sophistication_instructions="Categorize as either \"Sophisticated\" or \"Beginner\" based on complexity of language, technical depth, advanced features, level of expertise implied, and specificity suggesting familiarity with AI prompts.",
    use_case_instructions="Classify into one of these 6 categories: personal use, productivity, entertainment, professional, educational, communications"
)
