SOPHISTICATION_PROMPT = """
You are an expert product manager managing a no-code AI webapp builder. You want to understand what your users are trying to accomplish with your builder.

Analyze the following AI prompts for a no-code AI webapp builder and categorize its sophistication level.

Score the prompt on a scale of 1 to 5, where 1 is the simplest and 5 is the most sophisticated.

The most sophisticated prompts (higher score) will be:
- Mentioning advanced features, dev tools, or concepts 
- Specific about functionality, features, or concepts
- Shows a good understanding of how AI prompting works

The simplest prompts (lower score) will be:
- Vague, not specific about functionality, features, or concepts
- Shows a poor understanding of how AI prompting works

Content: {content}

Respond with only ONE number. Do not add any other text. 
"""

USE_CASE_PROMPT = """
You are an expert product manager managing a no-code AI webapp builder. You want to understand what your users are trying to accomplish with your builder.

Analyze the following user prompts for the builder, and classify its use case category.

Classify into ONE of these 5 categories:
- personal use: such as diet trackers, workout generators, personal planning, or other lifestyle tools.
- productivity: such as note-taking, calendar management, task organization, tools for studying and research.
- entertainment: such as story creation, games, creative content. it is usually shared with others. It doesn't have utility.
- professional: such as storefronts, portfolios, resumes, newsletters, business tools. it is generally shared with others and used for the user to generate wealth.
- communications: such as invitations, birthday cards, announcements, social messages. It is not used for professional or educational purposes.

Content: {content}

Respond with only ONE category name from the list above that best fits it. Do not add any other text. 
"""
